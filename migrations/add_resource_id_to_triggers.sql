-- Migration: Add resource_id column to triggers table
-- This migration adds the resource_id field needed for proper Google Calendar webhook management

-- Add the resource_id column
ALTER TABLE triggers 
ADD COLUMN resource_id VARCHAR(255);

-- Add index for the new column
CREATE INDEX idx_triggers_resource_id ON triggers(resource_id);

-- Add comment to document the column
COMMENT ON COLUMN triggers.resource_id IS 'Google Calendar webhook resource ID for database-based storage';

-- Update any existing triggers that have channel_id but no resource_id
-- Note: These will need to be re-registered to get proper resource_id values
UPDATE triggers 
SET resource_id = NULL 
WHERE channel_id IS NOT NULL AND resource_id IS NULL;
