-- Migration to fix trigger unique constraint
-- This migration updates the unique constraint to include trigger_type and trigger_name
-- as requested: (user_id, workflow_id, trigger_type, trigger_name)

BEGIN;

-- Drop the old constraints if they exist
ALTER TABLE triggers DROP CONSTRAINT IF EXISTS uq_user_workflow_trigger_name;
ALTER TABLE triggers DROP CONSTRAINT IF EXISTS uq_user_workflow_trigger_type;

-- Add the new constraint with trigger_type and trigger_name
ALTER TABLE triggers
ADD CONSTRAINT uq_user_workflow_trigger_type_name
UNIQUE (user_id, workflow_id, trigger_type, trigger_name);

-- Create index for better performance on the new constraint
CREATE INDEX IF NOT EXISTS idx_triggers_user_workflow_type_name
ON triggers (user_id, workflow_id, trigger_type, trigger_name);

COMMIT;

-- Verify the constraint was created
SELECT conname, contype, pg_get_constraintdef(oid) as definition
FROM pg_constraint
WHERE conrelid = 'triggers'::regclass
AND conname = 'uq_user_workflow_trigger_type_name';
