"""
Pydantic schemas for workflow execution operations.

This module contains all Pydantic models for workflow execution requests
and responses.
"""

from typing import Dict, Any, List

from pydantic import BaseModel, Field, ConfigDict


class WorkflowExecutionRequest(BaseModel):
    """Schema for workflow execution requests."""

    approval: bool = Field(
        ..., description="Whether the workflow execution is approved"
    )
    payload: Dict[str, Any] = Field(..., description="Workflow execution payload")
    user_id: str = Field(..., description="ID of the user executing the workflow")
    workflow_id: str = Field(..., description="ID of the workflow to execute")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "approval": False,
                "payload": {
                    "user_dependent_fields": [
                        "calendar_event_id",
                        "event_title",
                        "attendees",
                    ],
                    "user_payload_template": {
                        "calendar_event_id": {
                            "transition_id": "transition-1",
                            "value": "event-123",
                        },
                        "event_title": {
                            "transition_id": "transition-1",
                            "value": "Team Meeting",
                        },
                        "attendees": {
                            "transition_id": "transition-1",
                            "value": ["<EMAIL>", "<EMAIL>"],
                        },
                    },
                },
                "user_id": "user-123",
                "workflow_id": "workflow-456",
            }
        }
    )


class WorkflowExecutionResponse(BaseModel):
    """Schema for workflow execution responses."""

    correlationId: str = Field(
        ..., description="Correlation ID for tracking the execution"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"correlationId": "b0bad8e3-0470-4797-af64-f1e010c484a0"}
        }
    )


class WorkflowPayload(BaseModel):
    """Schema for workflow payload structure."""

    user_dependent_fields: List[str] = Field(
        ..., description="List of fields that depend on user input"
    )
    user_payload_template: Dict[str, Dict[str, Any]] = Field(
        ..., description="Template for user payload"
    )


class WorkflowPayloadField(BaseModel):
    """Schema for individual workflow payload fields."""

    transition_id: str = Field(
        ..., description="ID of the transition this field belongs to"
    )
    value: Any = Field(..., description="Value of the field")


class Workflow(BaseModel):
    """Schema for workflow information."""

    id: str = Field(..., description="Unique identifier for the workflow")
    name: str = Field(..., description="Name of the workflow")
    description: str = Field(..., description="Description of the workflow")

    model_config = ConfigDict(from_attributes=True)


class WorkflowCreate(BaseModel):
    """Schema for creating a new workflow."""

    name: str = Field(..., description="Name of the workflow")
    description: str = Field(..., description="Description of the workflow")


class WorkflowUpdate(BaseModel):
    """Schema for updating an existing workflow."""

    name: str = Field(None, description="Name of the workflow")
    description: str = Field(None, description="Description of the workflow")


class WorkflowResponse(BaseModel):
    """Schema for workflow API responses."""

    id: str = Field(..., description="Unique identifier for the workflow")
    name: str = Field(..., description="Name of the workflow")
    description: str = Field(..., description="Description of the workflow")

    model_config = ConfigDict(from_attributes=True)
