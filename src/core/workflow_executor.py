"""
Workflow Executor - Handles execution of workflows when triggers are activated.

This module provides the WorkflowExecutor class that fetches workflow details
and executes workflows when triggers are activated.
"""

import json
from typing import Dict, Any, Optional
import httpx
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.config import get_settings
from src.utils.http_client import get_workflow_http_client
from src.utils.field_converter import field_converter, field_extractor
from src.database.models import TriggerExecution

logger = structlog.get_logger(__name__)


class WorkflowExecutor:
    """
    Handles workflow fetching and execution requests to the workflow service.

    This class is responsible for:
    1. Fetching workflow details from the workflow service
    2. Formatting trigger event data into proper workflow execution format
    3. Making HTTP requests to execute workflows
    4. Storing execution results in the database
    """

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """Initialize the workflow executor."""
        self.settings = get_settings()
        self.client = None  # Will be initialized lazily
        self.db_session = db_session

    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create the HTTP client."""
        if self.client is None:
            self.client = await get_workflow_http_client()
        return self.client

    async def fetch_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch workflow details from the workflow service.

        Args:
            workflow_id: ID of the workflow to fetch

        Returns:
            Dict[str, Any]: Workflow details, or None if failed
        """
        try:
            logger.info("Fetching workflow details", workflow_id=workflow_id)

            # Get HTTP client and make GET request to fetch workflow
            client = await self._get_client()
            response = await client.get(
                f"https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/{workflow_id}",
                headers={"accept": "application/json"},
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    workflow_data = result.get("workflow")
                    logger.info(
                        "Workflow fetched successfully",
                        workflow_id=workflow_id,
                        workflow_name=workflow_data.get("name"),
                    )
                    return workflow_data
                else:
                    logger.error(
                        "Workflow fetch failed - API returned success=false",
                        workflow_id=workflow_id,
                        message=result.get("message"),
                    )
                    return None
            else:
                logger.error(
                    "Workflow fetch failed",
                    workflow_id=workflow_id,
                    status_code=response.status_code,
                    response=response.text,
                )
                return None

        except Exception as e:
            logger.error(
                "Error fetching workflow", workflow_id=workflow_id, error=str(e)
            )
            return None

    async def execute_workflow_with_fetch(
        self,
        trigger_execution: TriggerExecution,
        user_id: str,
        workflow_id: str,
        event_data: Dict[str, Any],
        db_session: Optional[AsyncSession] = None,
    ) -> Optional[str]:
        """
        Fetch workflow details and execute the workflow with event data.

        Args:
            db_session: Database session for storing execution results
            trigger_execution: TriggerExecution record to update
            user_id: ID of the user who owns the workflow
            workflow_id: ID of the workflow to execute
            event_data: Event data from the trigger

        Returns:
            str: Correlation ID from the workflow service, or None if failed
        """
        try:
            # Use the provided db_session or the one from initialization
            session_to_use = db_session if db_session else self.db_session
            if not session_to_use:
                logger.error("No database session provided for workflow execution.")
                return None

            # Step 1: Fetch workflow details
            workflow_data = await self.fetch_workflow(workflow_id)
            if not workflow_data:
                trigger_execution.status = "failed"
                trigger_execution.error_message = "Failed to fetch workflow details"
                await session_to_use.commit()
                return None

            # Step 2: Execute the workflow
            correlation_id = await self.execute_workflow(
                user_id=user_id,
                workflow_id=workflow_id,
                workflow_data=workflow_data,
                event_data=event_data,
            )

            # Step 3: Update execution record
            if correlation_id:
                trigger_execution.workflow_execution_id = correlation_id
                trigger_execution.status = "success"
                logger.info(
                    "Workflow execution completed successfully",
                    show_full_details=True,
                    correlation_id=correlation_id,
                    trigger_execution_id=str(trigger_execution.id),
                    user_id=user_id,
                    workflow_id=workflow_id,
                    workflow_name=workflow_data.get("name", "Unknown"),
                    execution_status="success",
                )
            else:
                trigger_execution.status = "failed"
                trigger_execution.error_message = "Workflow execution failed"
                logger.error(
                    "Workflow execution failed",
                    show_full_details=True,
                    trigger_execution_id=str(trigger_execution.id),
                    user_id=user_id,
                    workflow_id=workflow_id,
                    workflow_name=workflow_data.get("name", "Unknown"),
                    execution_status="failed",
                )

            await session_to_use.commit()
            return correlation_id

        except Exception as e:
            logger.error(
                "Error in workflow execution with fetch",
                error=str(e),
                trigger_execution_id=str(trigger_execution.id),
            )
            trigger_execution.status = "failed"
            trigger_execution.error_message = f"Execution error: {str(e)}"
            await session_to_use.commit()
            return None

    async def execute_workflow(
        self,
        user_id: str,
        workflow_id: str,
        workflow_data: Dict[str, Any],
        event_data: Dict[str, Any],
    ) -> Optional[str]:
        """
        Execute a workflow with the provided event data.

        Args:
            user_id: ID of the user who owns the workflow
            workflow_id: ID of the workflow to execute
            workflow_data: Complete workflow configuration from workflow service
            event_data: Event data from the trigger

        Returns:
            str: Correlation ID from the workflow service, or None if failed
        """
        try:
            # Transform event data into workflow payload format
            payload = self._transform_event_to_payload(event_data, workflow_data)

            # Prepare the workflow execution request
            request_data = {
                "approval": False,
                "payload": payload,
                "user_id": user_id,
                "workflow_id": workflow_id,
            }

            # Log the request data for debugging
            logger.info(
                "Executing workflow",
                show_full_details=True,
                workflow_id=workflow_id,
                user_id=user_id,
                payload=payload,
                request_data=request_data,
            )

            # Make the HTTP request to workflow execution service
            client = await self._get_client()
            response = await client.post(
                f"{self.settings.workflow_service_url}/api/v1/workflow-execute/server/execute",
                json=request_data,
            )

            if response.status_code == 202:
                result = response.json()
                correlation_id = result.get("correlationId")

                logger.info(
                    "Workflow execution initiated successfully",
                    show_full_details=True,
                    correlation_id=correlation_id,
                    user_id=user_id,
                    workflow_id=workflow_id,
                    status_code=response.status_code,
                    response_data=result,
                    execution_status="initiated",
                )

                return correlation_id
            else:
                logger.error(
                    "Workflow execution failed",
                    show_full_details=True,
                    status_code=response.status_code,
                    response=response.text,
                    user_id=user_id,
                    workflow_id=workflow_id,
                    execution_status="failed",
                )
                return None

        except Exception as e:
            logger.error(
                "Error executing workflow",
                error=str(e),
                user_id=user_id,
                workflow_id=workflow_id,
            )
            return None

    def _transform_event_to_payload(
        self, event_data: Dict[str, Any], workflow_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Transform trigger event data into workflow payload format.

        Args:
            event_data: Event data from the trigger
            workflow_data: Workflow configuration from workflow service

        Returns:
            Dict[str, Any]: Formatted payload for workflow execution
        """
        # Check if this is a scheduler event with input values
        scheduler_input_values = event_data.get("scheduler_input_values")

        if scheduler_input_values:
            # Use scheduler input values with template processing
            event_fields = field_extractor.extract_input_values(
                scheduler_input_values, event_data=event_data
            )
        else:
            # Check if trigger has input_values configuration
            trigger_config = event_data.get("trigger_config", {})
            trigger_input_values = trigger_config.get("input_values")

            if trigger_input_values:
                # Use trigger input values with template processing
                event_fields = field_extractor.extract_input_values(
                    trigger_input_values, event_data=event_data
                )
            elif trigger_config.get("selected_event_fields"):
                # Use selected event fields mapping
                calendar_event_data = event_data.get("data", {})
                event_fields = field_extractor.extract_mapped_fields(
                    calendar_event_data, trigger_config["selected_event_fields"]
                )
            else:
                # Fall back to default field extraction
                event_fields = field_extractor.extract_basic_event_fields(event_data)

        # Get start nodes from workflow data to determine required fields
        start_nodes = workflow_data.get("start_nodes", [])

        # Create user payload template with event data
        user_payload_template = {}
        user_dependent_fields = []

        # Map event fields to workflow start nodes
        for start_node in start_nodes:
            field_name = start_node.get("field")
            field_type = start_node.get("type")
            transition_id = start_node.get("transition_id")

            if field_name and transition_id:
                user_dependent_fields.append(field_name)

                # Use event data if available, otherwise use a default value based on field type
                if field_name in event_fields:
                    field_value = event_fields[field_name]
                else:
                    # Generate appropriate default value based on field type
                    field_value = field_converter.get_default_value_for_type(
                        field_type, field_name
                    )

                user_payload_template[field_name] = {
                    "transition_id": transition_id,
                    "value": field_value,
                }

        # If no start nodes found, use default mapping from event fields
        if not user_dependent_fields and event_fields:
            for field_name, field_value in event_fields.items():
                if field_value is not None:  # Only include non-null values
                    user_dependent_fields.append(field_name)
                    user_payload_template[field_name] = {
                        "transition_id": f"transition-{field_name}-default",
                        "value": field_value,
                    }

        return {
            "user_dependent_fields": user_dependent_fields,
            "user_payload_template": user_payload_template,
        }

    async def close(self):
        """Close the HTTP client."""
        if self.client is not None:
            await self.client.aclose()
