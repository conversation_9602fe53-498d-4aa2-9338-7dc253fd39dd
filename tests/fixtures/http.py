"""
HTTP fixtures for testing the Trigger Service API.

This module provides HTTP-related fixtures for testing API endpoints,
including test clients, mock responses, and authentication fixtures.
"""

from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient, Response

from src.main import app


@pytest.fixture
def test_client() -> TestClient:
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
async def async_test_client() -> AsyncClient:
    """Create an async test client for the FastAPI application."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def auth_headers() -> Dict[str, str]:
    """Authentication headers for testing."""
    return {"Authorization": "Bearer test-api-key", "X-API-Key": "test-api-key"}


@pytest.fixture
def invalid_auth_headers() -> Dict[str, str]:
    """Invalid authentication headers for testing."""
    return {"Authorization": "Bearer invalid-key", "X-API-Key": "invalid-key"}


@pytest.fixture
def correlation_id() -> str:
    """Sample correlation ID for testing."""
    return "test-correlation-123"


class MockHttpResponse:
    """Mock HTTP response for testing external API calls."""

    def __init__(
        self,
        status_code: int = 200,
        json_data: Optional[Dict[str, Any]] = None,
        text: str = "",
        headers: Optional[Dict[str, str]] = None,
    ):
        self.status_code = status_code
        self._json_data = json_data or {}
        self.text = text
        self.headers = headers or {}

    def json(self) -> Dict[str, Any]:
        return self._json_data

    def raise_for_status(self) -> None:
        if self.status_code >= 400:
            raise Exception(f"HTTP {self.status_code}")


@pytest.fixture
def mock_successful_response() -> MockHttpResponse:
    """Mock successful HTTP response."""
    return MockHttpResponse(
        status_code=200,
        json_data={"correlationId": "test-correlation-123", "status": "success"},
    )


@pytest.fixture
def mock_error_response() -> MockHttpResponse:
    """Mock error HTTP response."""
    return MockHttpResponse(
        status_code=500,
        json_data={"error": "Internal server error"},
        text="Internal server error",
    )


@pytest.fixture
def mock_timeout_response() -> MockHttpResponse:
    """Mock timeout HTTP response."""
    return MockHttpResponse(
        status_code=408, json_data={"error": "Request timeout"}, text="Request timeout"
    )


@pytest.fixture
def mock_auth_error_response() -> MockHttpResponse:
    """Mock authentication error response."""
    return MockHttpResponse(
        status_code=401, json_data={"error": "Unauthorized"}, text="Unauthorized"
    )


class MockAsyncHttpClient:
    """Mock async HTTP client for testing external service calls."""

    def __init__(self):
        self.post = AsyncMock()
        self.get = AsyncMock()
        self.put = AsyncMock()
        self.delete = AsyncMock()
        self.close = AsyncMock()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


@pytest.fixture
def mock_http_client() -> MockAsyncHttpClient:
    """Create a mock HTTP client."""
    return MockAsyncHttpClient()


@pytest.fixture
def google_calendar_webhook_payload() -> Dict[str, Any]:
    """Sample Google Calendar webhook payload."""
    return {
        "kind": "api#channel",
        "id": "test-channel-123",
        "resourceId": "test-resource-456",
        "resourceUri": "https://www.googleapis.com/calendar/v3/calendars/<EMAIL>/events",
        "token": "test-token",
        "expiration": "1640995200000",
    }


@pytest.fixture
def google_calendar_event_data() -> Dict[str, Any]:
    """Sample Google Calendar event data."""
    return {
        "kind": "calendar#event",
        "id": "test-event-123",
        "status": "confirmed",
        "htmlLink": "https://calendar.google.com/event?eid=test",
        "created": "2024-01-15T08:00:00.000Z",
        "updated": "2024-01-15T08:30:00.000Z",
        "summary": "Test Meeting",
        "description": "This is a test meeting",
        "start": {
            "dateTime": "2024-01-15T10:00:00-08:00",
            "timeZone": "America/Los_Angeles",
        },
        "end": {
            "dateTime": "2024-01-15T11:00:00-08:00",
            "timeZone": "America/Los_Angeles",
        },
        "attendees": [
            {"email": "<EMAIL>", "responseStatus": "accepted"},
            {"email": "<EMAIL>", "responseStatus": "needsAction"},
        ],
        "organizer": {
            "email": "<EMAIL>",
            "displayName": "Test Organizer",
        },
    }


@pytest.fixture
def workflow_execution_request() -> Dict[str, Any]:
    """Sample workflow execution request."""
    return {
        "approval": False,
        "payload": {
            "trigger_type": "google_calendar",
            "event_type": "created",
            "event_data": {
                "event_id": "test-event-123",
                "summary": "Test Meeting",
                "start_time": "2024-01-15T10:00:00-08:00",
                "end_time": "2024-01-15T11:00:00-08:00",
            },
        },
        "user_id": "test-user-123",
        "workflow_id": "test-workflow-456",
    }


@pytest.fixture
def workflow_execution_response() -> Dict[str, Any]:
    """Sample workflow execution response."""
    return {
        "correlationId": "test-correlation-123",
        "status": "initiated",
        "workflowId": "test-workflow-456",
        "executionId": "exec-789",
    }


@pytest.fixture
def mock_google_credentials() -> Dict[str, Any]:
    """Mock Google OAuth2 credentials."""
    return {
        "access_token": "mock-access-token",
        "refresh_token": "mock-refresh-token",
        "token_uri": "https://oauth2.googleapis.com/token",
        "client_id": "mock-client-id",
        "client_secret": "mock-client-secret",
        "scopes": ["https://www.googleapis.com/auth/calendar.readonly"],
    }


@pytest.fixture
def mock_auth_service_response() -> Dict[str, Any]:
    """Mock auth service response."""
    return {
        "credentials": {
            "google_calendar": {
                "access_token": "mock-access-token",
                "refresh_token": "mock-refresh-token",
                "expires_at": "2024-12-31T23:59:59Z",
            }
        },
        "user_id": "test-user-123",
    }


# Network error fixtures
@pytest.fixture
def connection_error():
    """Mock connection error."""
    import httpx

    return httpx.ConnectError("Connection failed")


@pytest.fixture
def timeout_error():
    """Mock timeout error."""
    import httpx

    return httpx.TimeoutException("Request timed out")


@pytest.fixture
def http_status_error():
    """Mock HTTP status error."""
    import httpx

    mock_request = MagicMock()
    mock_response = MagicMock()
    mock_response.status_code = 500
    return httpx.HTTPStatusError(
        "Server error", request=mock_request, response=mock_response
    )
